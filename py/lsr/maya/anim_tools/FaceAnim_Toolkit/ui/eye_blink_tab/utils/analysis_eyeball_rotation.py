# -*- coding: utf-8 -*-

import math
import random
from maya import cmds
from maya import OpenMaya


class AnalysisEyeballRotation(object):

    def __init__(self, *args, **kwargs):
        # 测试数据：添加一些有旋转变化的矩阵用于测试
        frame_matrix_dict = {
            1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
            2: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.866, -0.5, 0, 0, 0.5, 0.866, 0, 0, 0, 0, 1]),  # 30度旋转
            3: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.707, -0.707, 0, 0, 0.707, 0.707, 0, 0, 0, 0, 1]),  # 45度旋转
            4: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.5, -0.866, 0, 0, 0.866, 0.5, 0, 0, 0, 0, 1]),  # 60度旋转
            5: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, 0, 0, 1]),  # 90度旋转
            6: OpenMaya.MMatrix([1, 0, 0, 0, 0, -0.5, -0.866, 0, 0, 0.866, -0.5, 0, 0, 0, 0, 1])  # 120度旋转
        }
        frame_spacing = 2  # n帧间隔
        angle_threshold = 15  # 角度阈值（度）

        self.frame_matrix_dict = frame_matrix_dict
        self.frame_spacing = frame_spacing
        self.angle_threshold = angle_threshold

    def extract_rotation_from_matrix(self, matrix):
        """
        从MMatrix中提取旋转信息（欧拉角）

        Args:
            matrix (OpenMaya.MMatrix): 输入矩阵

        Returns:
            tuple: (rx, ry, rz) 旋转角度（度）
        """
        # 创建变换矩阵
        transform_matrix = OpenMaya.MTransformationMatrix(matrix)

        # 获取欧拉旋转
        euler_rotation = transform_matrix.eulerRotation()

        # 转换为度数
        rx = math.degrees(euler_rotation.x)
        ry = math.degrees(euler_rotation.y)
        rz = math.degrees(euler_rotation.z)

        return (rx, ry, rz)

    def calculate_rotation_difference(self, rotation1, rotation2):
        """
        计算两个旋转之间的角度差异

        Args:
            rotation1 (tuple): 第一个旋转 (rx, ry, rz)
            rotation2 (tuple): 第二个旋转 (rx, ry, rz)

        Returns:
            float: 总的旋转角度差异（度）
        """
        # 计算每个轴的角度差
        diff_x = rotation2[0] - rotation1[0]
        diff_y = rotation2[1] - rotation1[1]
        diff_z = rotation2[2] - rotation1[2]

        # 处理角度环绕问题（-180到180度）
        def normalize_angle(angle):
            while angle > 180:
                angle -= 360
            while angle < -180:
                angle += 360
            return angle

        diff_x = normalize_angle(diff_x)
        diff_y = normalize_angle(diff_y)
        diff_z = normalize_angle(diff_z)

        # 计算总的旋转幅度（欧几里得距离）
        total_rotation = math.sqrt(diff_x*diff_x + diff_y*diff_y + diff_z*diff_z)

        return total_rotation

    def analyze_blink_amplitude(self, start_frame, frames_to_analyze=10):
        """
        分析眨眼开始后指定帧数内的最大旋转幅度

        Args:
            start_frame (int): 眨眼开始帧
            frames_to_analyze (int): 要分析的帧数（默认10帧）

        Returns:
            float: 最大旋转幅度（度）
        """
        max_amplitude = 0.0
        base_frame = start_frame - self.frame_spacing

        # 确保基准帧存在
        if base_frame not in self.frame_matrix_dict:
            return max_amplitude

        base_matrix = self.frame_matrix_dict[base_frame]
        base_rotation = self.extract_rotation_from_matrix(base_matrix)

        # 分析后续帧的旋转幅度
        for i in range(frames_to_analyze):
            check_frame = start_frame + i
            if check_frame not in self.frame_matrix_dict:
                continue

            check_matrix = self.frame_matrix_dict[check_frame]
            check_rotation = self.extract_rotation_from_matrix(check_matrix)

            # 计算与基准帧的旋转差异
            rotation_diff = self.calculate_rotation_difference(base_rotation, check_rotation)
            max_amplitude = max(max_amplitude, rotation_diff)

        return max_amplitude

    def analyze_rotation_changes(self):
        """
        分析旋转变化并输出标记

        完整眨眼检测逻辑：
        - 如果旋转没有超过阈值，不输出任何内容
        - 如果首次出现超过阈值，输出0（眨眼开始）
        - 分析眨眼开始后10帧内的最大旋转幅度：
          * 如果 < 1.5倍阈值：小幅度眨眼 - 随机1-2帧输出1，随机+2-3帧输出0
          * 如果 ≥ 1.5倍阈值：大幅度眨眼 - 随机2-3帧输出1，随机+5-6帧输出0
        - 输出0后，等待2n帧才能再次检测新的眨眼
        - 在等待期间不输出任何内容

        Returns:
            dict: {frame: marker} 其中marker为0或1
                  0表示眨眼开始，1表示眨眼结束
        """
        result_markers = {}
        frames = sorted(self.frame_matrix_dict.keys())

        # 状态跟踪
        last_end_frame = None  # 上次输出0完成周期的帧号
        cooldown_frames = 2 * self.frame_spacing  # 冷却期：2n帧
        pending_blinks = {}  # 待处理的眨眼：{start_frame: (amplitude, end_offset, complete_offset)}

        print("开始分析旋转变化...")
        print(f"帧间隔: {self.frame_spacing}, 角度阈值: {self.angle_threshold}度")
        print(f"眨眼幅度判断: <{1.5 * self.angle_threshold}度=小幅度眨眼, ≥{1.5 * self.angle_threshold}度=大幅度眨眼")
        print(f"随机时机: 小幅度(1-2帧输出1, +2-3帧输出0), 大幅度(2-3帧输出1, +5-6帧输出0)")
        print(f"眨眼结束后等待期: {cooldown_frames}帧")
        print(f"时间偏移修正: 所有标记向前偏移{self.frame_spacing + 1}帧（frame_spacing + 1），与实际眼动时机同步")

        for current_frame in frames:
            # 计算对比帧（当前帧 - n帧）
            compare_frame = current_frame - self.frame_spacing

            # 检查对比帧是否存在
            if compare_frame not in self.frame_matrix_dict:
                continue

            # 处理待处理的眨眼标记
            blinks_to_remove = []
            for start_frame, blink_data in pending_blinks.items():
                amplitude, end_offset, complete_offset = blink_data
                frame_offset = current_frame - start_frame

                # 判断眨眼类型和输出时机
                amplitude_threshold = 1.5 * self.angle_threshold

                if amplitude < amplitude_threshold:
                    # 小幅度眨眼：随机时机（考虑偏移+额外1帧）
                    if frame_offset == end_offset:
                        output_frame = current_frame - self.frame_spacing - 1
                        result_markers[output_frame] = 1
                        print(f"帧 {output_frame}: 小幅度眨眼结束，标记1（偏移修正+1帧，随机第{end_offset}帧）")
                    elif frame_offset == complete_offset:
                        output_frame = current_frame - self.frame_spacing - 1
                        result_markers[output_frame] = 0
                        last_end_frame = current_frame
                        print(f"帧 {output_frame}: 小幅度眨眼周期完成，标记0（偏移修正+1帧，随机第{complete_offset}帧），开始{cooldown_frames}帧等待期")
                        blinks_to_remove.append(start_frame)
                else:
                    # 大幅度眨眼：随机时机（考虑偏移+额外1帧）
                    if frame_offset == end_offset:
                        output_frame = current_frame - self.frame_spacing - 1
                        result_markers[output_frame] = 1
                        print(f"帧 {output_frame}: 大幅度眨眼结束，标记1（偏移修正+1帧，随机第{end_offset}帧）")
                    elif frame_offset == complete_offset:
                        output_frame = current_frame - self.frame_spacing - 1
                        result_markers[output_frame] = 0
                        last_end_frame = current_frame
                        print(f"帧 {output_frame}: 大幅度眨眼周期完成，标记0（偏移修正+1帧，随机第{complete_offset}帧），开始{cooldown_frames}帧等待期")
                        blinks_to_remove.append(start_frame)

            # 移除已处理完的眨眼
            for start_frame in blinks_to_remove:
                del pending_blinks[start_frame]

            # 检查是否在冷却期内或有待处理的眨眼
            if (last_end_frame is not None and current_frame - last_end_frame < cooldown_frames) or pending_blinks:
                if pending_blinks:
                    print(f"帧 {current_frame}: 有待处理的眨眼，跳过新检测")
                else:
                    print(f"帧 {current_frame}: 在等待期内，跳过检测")
                continue

            # 获取当前帧和对比帧的矩阵
            current_matrix = self.frame_matrix_dict[current_frame]
            compare_matrix = self.frame_matrix_dict[compare_frame]

            # 提取旋转信息
            current_rotation = self.extract_rotation_from_matrix(current_matrix)
            compare_rotation = self.extract_rotation_from_matrix(compare_matrix)

            # 计算旋转差异
            rotation_diff = self.calculate_rotation_difference(compare_rotation, current_rotation)

            print(f"帧 {compare_frame} -> 帧 {current_frame}: 旋转差异 = {rotation_diff:.2f}度")

            # 判断当前是否超过阈值
            current_exceeds = rotation_diff > self.angle_threshold

            if current_exceeds:
                # 检测到眨眼开始（考虑偏移+额外1帧）
                blink_start_frame = current_frame - self.frame_spacing - 1
                result_markers[blink_start_frame] = 0

                # 分析眨眼幅度
                amplitude = self.analyze_blink_amplitude(current_frame, 10)
                amplitude_threshold = 1.5 * self.angle_threshold

                # 生成随机时机
                if amplitude < amplitude_threshold:
                    # 小幅度眨眼：随机第1或2帧输出1，然后随机+2或3帧输出0
                    end_offset = random.choice([1, 2])
                    complete_offset = end_offset + random.choice([2, 3])
                    blink_type = "小幅度"
                else:
                    # 大幅度眨眼：随机第2或3帧输出1，然后随机+5或6帧输出0
                    end_offset = random.choice([2, 3])
                    complete_offset = end_offset + random.choice([5, 6])
                    blink_type = "大幅度"

                pending_blinks[current_frame] = (amplitude, end_offset, complete_offset)

                print(f"  -> 眨眼开始! 在帧{blink_start_frame}标记0（偏移修正+1帧）")
                print(f"     眨眼幅度: {amplitude:.2f}度 ({blink_type}眨眼)")
                print(f"     随机时机: 第{end_offset}帧输出1，第{complete_offset}帧输出0")
            else:
                # 未超过阈值
                print(f"  -> 未超过阈值")

        return result_markers

    def get_analysis_result(self):
        """
        获取分析结果的便捷方法

        Returns:
            dict: 分析结果
        """
        return self.analyze_rotation_changes()

    def print_analysis_summary(self):
        """
        打印分析摘要
        """
        markers = self.analyze_rotation_changes()

        print("\n=== 分析摘要 ===")
        print(f"总帧数: {len(self.frame_matrix_dict)}")
        print(f"帧间隔: {self.frame_spacing}")
        print(f"角度阈值: {self.angle_threshold}度")
        print(f"检测到的标记: {len(markers)}")

        if markers:
            print("\n标记结果:")
            for frame in sorted(markers.keys()):
                marker_type = "首次超过阈值" if markers[frame] == 0 else "恢复到阈值内"
                print(f"  帧 {frame}: {markers[frame]} ({marker_type})")
        else:
            print("未检测到超过阈值的旋转变化")


def eye_blink_run():
    """
    分析指定对象在指定帧范围内的旋转变化
    """

    def debug_analysis_result(analysis_result):
        """
        调试分析结果，帮助诊断问题
        """
        print("\n=== 调试分析结果 ===")
        if not analysis_result:
            print("❌ 分析结果为空！")
            print("可能的原因：")
            print("1. 选中的对象没有足够的旋转变化")
            print("2. 角度阈值设置过高")
            print("3. 帧间隔设置不合适")
            print("4. 动画数据获取失败")
            return False

        print(f"✅ 检测到 {len(analysis_result)} 个标记")

        # 分析标记模式
        frames_sorted = sorted(analysis_result.items())
        zeros = [f for f, v in frames_sorted if v == 0]
        ones = [f for f, v in frames_sorted if v == 1]

        print(f"0标记(眨眼开始/周期完成): {len(zeros)} 个 - 帧: {zeros}")
        print(f"1标记(眨眼结束): {len(ones)} 个 - 帧: {ones}")

        # 检查模式是否正确
        if len(zeros) == 0 and len(ones) == 0:
            print("❌ 没有检测到任何眨眼标记")
            return False
        elif len(ones) == 0:
            print("⚠️  只检测到0标记，没有1标记 - 可能眨眼没有完成")
        elif len(zeros) < len(ones):
            print("⚠️  0标记少于1标记 - 可能缺少眨眼开始标记")
        elif len(zeros) > len(ones) + 1:
            print("⚠️  0标记过多 - 可能有多余的周期完成标记")
        else:
            print("✅ 标记模式看起来正常")

        # 分析眨眼周期
        print("\n眨眼周期分析:")
        for i, (frame, value) in enumerate(frames_sorted):
            if value == 0:
                if i > 0 and frames_sorted[i - 1][1] == 1:
                    print(f"  帧 {frame}: 眨眼周期完成")
                else:
                    print(f"  帧 {frame}: 眨眼开始")
            else:
                print(f"  帧 {frame}: 眨眼结束")

        return True

    def analyze_object_rotation(object_name, frame_spacing=2, angle_threshold=15, start_frame=1, end_frame=100):
        """
        分析Maya对象的旋转变化

        Args:
            object_name (str): Maya对象名称
            frame_spacing (int): 帧间隔
            angle_threshold (float): 角度阈值（度）
            start_frame (int): 起始帧
            end_frame (int): 结束帧

        Returns:
            dict: 分析结果标记
        """
        # 检查对象是否存在
        if not cmds.objExists(object_name):
            raise RuntimeError(f"对象 '{object_name}' 不存在")

        print(f"开始分析对象 '{object_name}' 的旋转变化...")
        print(f"帧范围: {start_frame} - {end_frame}")
        print(f"帧间隔: {frame_spacing}, 角度阈值: {angle_threshold}度")

        # 获取矩阵数据
        frame_matrix_dict = get_object_matrices_from_timeline(object_name, start_frame, end_frame)

        # 创建分析器
        analyzer = AnalysisEyeballRotation()
        analyzer.frame_matrix_dict = frame_matrix_dict
        analyzer.frame_spacing = frame_spacing
        analyzer.angle_threshold = angle_threshold

        # 执行分析
        result = analyzer.analyze_rotation_changes()

        # 打印摘要
        analyzer.print_analysis_summary()

        return result

    def get_object_matrices_from_timeline(object_name, start_frame=1, end_frame=100):
        """
        从Maya时间轴获取对象的矩阵数据

        Args:
            object_name (str): Maya对象名称
            start_frame (int): 起始帧
            end_frame (int): 结束帧

        Returns:
            dict: {frame: MMatrix} 帧和矩阵的字典
        """
        frame_matrix_dict = {}

        # 保存当前时间
        current_time = cmds.currentTime(query=True)

        try:
            for frame in range(start_frame, end_frame + 1):
                # 设置当前帧
                cmds.currentTime(frame)

                # 获取局部矩阵
                matrix_list = cmds.getAttr(f'{object_name}.matrix')

                # 转换为MMatrix (正确的矩阵创建方法)
                matrix = OpenMaya.MMatrix(matrix_list)

                frame_matrix_dict[frame] = matrix

        finally:
            # 恢复原始时间
            cmds.currentTime(current_time)

        return frame_matrix_dict

    def create_keyframes_from_analysis(object_name, analysis_result, attribute_name="visibility"):
        """
        根据分析结果在Maya中创建关键帧

        完整眨眼周期逻辑（已修正时间偏移）：
        - 0 表示眨眼开始或眨眼周期完成
        - 1 表示眨眼结束
        - 所有标记都已向前偏移frame_spacing帧，与实际眼动时机同步
        - 完整周期：0(开始) → 1(结束) → 0(周期完成)

        Args:
            object_name (str): Maya对象名称
            analysis_result (dict): 分析结果
            attribute_name (str): 要设置关键帧的属性名
        """
        if not analysis_result:
            print("没有分析结果，跳过关键帧创建")
            return

        print(f"在对象 '{object_name}' 的 '{attribute_name}' 属性上创建关键帧...")
        print("完整眨眼周期: 0(开始/周期完成) → 1(结束) → 0(周期完成)")

        # 保存当前时间
        current_time = cmds.currentTime(query=True)

        try:
            # 分析结果中的0和1的含义
            frames_sorted = sorted(analysis_result.items())

            # 清除对象原始动画帧
            cmds.cutKey(object_name,
                        time=(cmds.playbackOptions(q=True, minTime=True), cmds.playbackOptions(q=True, maxTime=True)),
                        attribute=attribute_name)

            for i, (frame, value) in enumerate(frames_sorted):
                # 设置当前帧
                cmds.currentTime(frame)

                # 设置属性值
                cmds.setAttr(f'{object_name}.{attribute_name}', value)

                # 创建关键帧
                cmds.setKeyframe(f'{object_name}.{attribute_name}')

                # 判断标记类型
                if value == 0:
                    # 判断是眨眼开始还是周期完成
                    if i > 0 and frames_sorted[i - 1][1] == 1:
                        marker_type = "眨眼周期完成"
                    else:
                        marker_type = "眨眼开始"
                else:
                    marker_type = "眨眼结束"

                print(f"  帧 {frame}: {attribute_name} = {value} ({marker_type})")

        finally:
            # 恢复原始时间
            cmds.currentTime(current_time)

        print("关键帧创建完成")

    # 获取选中对象
    selected_objects = cmds.ls(selection=True)
    if not selected_objects:
        raise RuntimeError("请先选择一个对象")

    object_name = selected_objects[0]
    name_space_list = object_name.split(":")
    full_name_space = ''
    for idx in range(len(name_space_list) - 1):
        full_name_space += name_space_list[idx] + ":"
    mh_eye_ball_joint_l = f"{full_name_space}:FACIAL_L_Eye"
    mh_eye_ball_joint_r = f"{full_name_space}:FACIAL_R_Eye"

    try:
        # 分析旋转变化
        result = analyze_object_rotation(
            object_name=mh_eye_ball_joint_l,
            frame_spacing=2,  # 2帧间隔
            angle_threshold=10,  # 10度阈值
            start_frame=int(cmds.playbackOptions(query=True, minTime=True)),
            end_frame=int(cmds.playbackOptions(query=True, maxTime=True))
        )

        # 调试分析结果
        debug_success = debug_analysis_result(result)

        # 根据结果创建关键帧（可选）
        if result and debug_success:
            eye_blink_ctrls = [f"{full_name_space}:CTRL_L_eye_blink", f"{full_name_space}:CTRL_R_eye_blink"]
            for eye_blink_ctrl in eye_blink_ctrls:
                create_keyframes_from_analysis(eye_blink_ctrl, result, "translateY")
            print(result)
        else:
            print("\n❌ 未检测到有效的眨眼动作")
            print("建议:")
            print("1. 检查选中对象是否有旋转动画")
            print("2. 调整angle_threshold参数（当前: 10度）")
            print("3. 调整frame_spacing参数（当前: 1帧）")
            print("4. 检查帧范围是否包含眨眼动画")

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()