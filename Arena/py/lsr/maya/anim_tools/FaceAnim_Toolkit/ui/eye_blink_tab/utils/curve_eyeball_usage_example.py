# -*- coding: utf-8 -*-

"""
曲线数据与眼球分析集成使用示例

这个文件展示了如何使用修改后的analysis_eyeball_rotation模块
来实现基于曲线数据的平滑眨眼动画过渡。
"""

from maya import cmds
from .analysis_eyeball_rotation import AnalysisEyeballRotation, analyze_eyeball_with_curve_data


def example_basic_curve_integration():
    """
    示例1：基本的曲线数据集成
    """
    print("\n=== 示例1：基本曲线数据集成 ===")
    
    # 创建示例曲线数据（0-1过渡）
    curve_data = [0.0, 0.1, 0.3, 0.6, 0.8, 0.95, 1.0]  # 7个采样点
    
    # 创建反转曲线数据（1-0过渡）
    reversed_curve_data = [1.0, 0.9, 0.7, 0.4, 0.2, 0.05, 0.0]  # 7个采样点
    
    print(f"0-1过渡曲线数据: {curve_data}")
    print(f"1-0过渡曲线数据: {reversed_curve_data}")
    
    # 创建测试矩阵数据（模拟眨眼动作）
    from maya import OpenMaya
    frame_matrix_dict = {
        1: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]),
        3: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0.707, -0.707, 0, 0, 0.707, 0.707, 0, 0, 0, 0, 1]),  # 45度旋转
        5: OpenMaya.MMatrix([1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, 0, 0, 1]),  # 90度旋转
        7: OpenMaya.MMatrix([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1])   # 回到原位
    }
    
    # 创建分析器并传入曲线数据
    analyzer = AnalysisEyeballRotation(
        frame_matrix_dict=frame_matrix_dict,
        curve_data=curve_data,
        reversed_curve_data=reversed_curve_data
    )
    analyzer.frame_spacing = 2
    analyzer.angle_threshold = 15
    
    # 执行分析
    result = analyzer.analyze_rotation_changes()
    
    print(f"\n分析结果: {len(result)} 个标记")
    for frame in sorted(result.keys()):
        print(f"  帧 {frame}: 值 {result[frame]:.3f}")
    
    return result


def example_bezier_curve_integration():
    """
    示例2：与贝塞尔曲线编辑器集成
    """
    print("\n=== 示例2：贝塞尔曲线编辑器集成 ===")
    
    try:
        # 导入贝塞尔曲线编辑器
        from ..bezier_curve_editor import BezierCurveWidget
        
        # 创建曲线编辑器实例
        curve_widget = BezierCurveWidget()
        
        # 添加一些关键帧来创建曲线
        curve_widget.keyframes = [
            {'time': 0.0, 'value': 0.0, 'in_tangent': (0.0, 0.0), 'out_tangent': (0.3, 0.0)},
            {'time': 0.5, 'value': 1.0, 'in_tangent': (-0.2, 0.0), 'out_tangent': (0.2, 0.0)},
            {'time': 1.0, 'value': 0.0, 'in_tangent': (-0.3, 0.0), 'out_tangent': (0.0, 0.0)}
        ]
        
        print("创建了贝塞尔曲线编辑器，包含3个关键帧")
        
        # 检查选中的Maya对象
        selected_objects = cmds.ls(selection=True)
        if not selected_objects:
            print("请先在Maya中选择一个有动画的对象")
            return None
            
        object_name = selected_objects[0]
        print(f"分析对象: {object_name}")
        
        # 使用曲线数据分析眼球旋转
        result = analyze_eyeball_with_curve_data(
            object_name=object_name,
            curve_widget=curve_widget,
            frame_spacing=2,
            angle_threshold=10,
            start_frame=int(cmds.playbackOptions(query=True, minTime=True)),
            end_frame=int(cmds.playbackOptions(query=True, maxTime=True))
        )
        
        print(f"\n分析结果: {len(result)} 个标记")
        for frame in sorted(result.keys()):
            print(f"  帧 {frame}: 值 {result[frame]:.3f}")
            
        return result
        
    except ImportError as e:
        print(f"无法导入贝塞尔曲线编辑器: {e}")
        print("请确保bezier_curve_editor模块可用")
        return None


def example_manual_curve_data():
    """
    示例3：手动提供曲线数据
    """
    print("\n=== 示例3：手动提供曲线数据 ===")
    
    # 检查选中的Maya对象
    selected_objects = cmds.ls(selection=True)
    if not selected_objects:
        print("请先在Maya中选择一个有动画的对象")
        return None
        
    object_name = selected_objects[0]
    print(f"分析对象: {object_name}")
    
    # 创建自定义曲线数据
    # 0-1过渡：慢启动，快结束
    curve_data = []
    for i in range(20):
        t = i / 19.0  # 0.0 到 1.0
        # 使用二次函数创建慢启动效果
        value = t * t
        curve_data.append(value)
    
    # 1-0过渡：快启动，慢结束
    reversed_curve_data = []
    for i in range(20):
        t = i / 19.0  # 0.0 到 1.0
        # 使用平方根函数创建快启动效果
        value = 1.0 - (t ** 0.5)
        reversed_curve_data.append(value)
    
    print(f"0-1过渡曲线: {len(curve_data)}个点，范围 {curve_data[0]:.3f} - {curve_data[-1]:.3f}")
    print(f"1-0过渡曲线: {len(reversed_curve_data)}个点，范围 {reversed_curve_data[0]:.3f} - {reversed_curve_data[-1]:.3f}")
    
    # 使用自定义曲线数据分析
    result = analyze_eyeball_with_curve_data(
        object_name=object_name,
        curve_data=curve_data,
        reversed_curve_data=reversed_curve_data,
        frame_spacing=2,
        angle_threshold=10,
        start_frame=int(cmds.playbackOptions(query=True, minTime=True)),
        end_frame=int(cmds.playbackOptions(query=True, maxTime=True))
    )
    
    print(f"\n分析结果: {len(result)} 个标记")
    for frame in sorted(result.keys()):
        print(f"  帧 {frame}: 值 {result[frame]:.3f}")
        
    return result


def example_apply_to_maya_object():
    """
    示例4：将结果应用到Maya对象
    """
    print("\n=== 示例4：应用到Maya对象 ===")
    
    # 运行示例3获取结果
    result = example_manual_curve_data()
    
    if not result:
        print("没有分析结果，跳过应用")
        return
    
    # 检查选中的Maya对象
    selected_objects = cmds.ls(selection=True)
    if not selected_objects:
        print("请先在Maya中选择一个对象")
        return
        
    target_object = selected_objects[0]
    attribute_name = "scaleY"  # 使用scaleY属性来模拟眨眼
    
    print(f"将结果应用到对象: {target_object}.{attribute_name}")
    
    # 保存当前时间
    current_time = cmds.currentTime(query=True)
    
    try:
        # 清除现有关键帧
        cmds.cutKey(target_object, attribute=attribute_name)
        
        # 应用分析结果
        for frame, value in sorted(result.items()):
            cmds.currentTime(frame)
            cmds.setAttr(f'{target_object}.{attribute_name}', value)
            cmds.setKeyframe(f'{target_object}.{attribute_name}')
            print(f"  帧 {frame}: {attribute_name} = {value:.3f}")
            
    finally:
        # 恢复原始时间
        cmds.currentTime(current_time)
    
    print("关键帧应用完成")


def run_all_examples():
    """
    运行所有示例
    """
    print("=== 曲线数据与眼球分析集成示例 ===")
    
    # 示例1：基本集成
    example_basic_curve_integration()
    
    # 示例2：贝塞尔曲线编辑器集成
    example_bezier_curve_integration()
    
    # 示例3：手动曲线数据
    example_manual_curve_data()
    
    # 示例4：应用到Maya对象
    example_apply_to_maya_object()
    
    print("\n=== 所有示例运行完成 ===")


if __name__ == "__main__":
    # 如果直接运行此文件，执行所有示例
    run_all_examples()
